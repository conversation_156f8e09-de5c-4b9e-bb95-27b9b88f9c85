import React, { useState, useContext, useCallback } from "react";
import { MyForm } from "@/components/MyForm";
import { Modal, message } from "antd";
import QrCodeLogin from "@/components/QrCodeLogin";
import { venderContext } from "./context/index";
import { useQuery } from "@/components/Hooks";
import "./login.less";

import { creatVender } from "@/service";

const VenderLogin = (props) => {
    const { setUserInfo, model } = useContext(venderContext);
    const [creatVenderType, setCreatVenderType] = useState("creat");
    const [creatVenderState, setCreatVenderState] = useState(false);
    const [openId, setOpenId] = useState(null);

    const [query, setQuery] = useQuery();
    console.log("Params==", query.venderCompanyId);

    let { referrer } = props.location;
    if (!referrer) {
        referrer = "/vender";
    }

    // 创建 vender 用户表单配置
    const creatVenderForm = [
        {
            type: "input",
            name: "venderName",
            attribute: {
                placeholder: "请填写用户名称",
                rule: {
                    required: "用户名称不能为空",
                    maxLength: {
                        value: 50,
                        message: "用户名称长度不能超过50个字符",
                    },
                },
                label: { value: "用户名称", required: true },
            },
        },
        {
            type: "input",
            name: "venderPhone",
            attribute: {
                placeholder: "请填写联系电话",
                rule: {
                    required: "联系方式不能为空",
                    pattern: {
                        value: /^(?:(?:\d{3}-)?\d{8}|^(?:\d{4}-)?\d{7,8})(?:-\d+)?$|^(0|86|17951)?(1[0-9])[0-9]{9}$/,
                        message: "请正确填写联系方式，手机或座机",
                    },
                },
                label: { value: "联系方式", required: true },
            },
        },

        {
            type: "input",
            name: "name",
            attribute: {
                placeholder: "供应商名称",
                rule: {
                    required: "供应商名称不能为空",
                    maxLength: {
                        value: 100,
                        message: "供应商名称长度不能超过100个字符",
                    },
                },
                label: { value: "供应商名称", required: true },
            },
        },
        {
            type: "input",
            name: "city",
            attribute: {
                placeholder: "请填写城市",
                rule: {
                    required: "城市不能为空",
                    maxLength: {
                        value: 100,
                        message: "城市不能超过100个字符",
                    },
                },
                label: { value: "城市", required: true },
            },
        },
        {
            type: "input",
            name: "address",
            attribute: {
                placeholder: "请填写地址",
                rule: {
                    required: "地址不能为空",
                    maxLength: {
                        value: 200,
                        message: "地址不能超过200个字符",
                    },
                },
                label: { value: "地址", required: true },
            },
        },
        {
            type: "input",
            name: "zipcode",
            attribute: {
                placeholder: "请填写邮编",
                rule: {
                    required: "地址不能为空",
                    pattern: {
                        value: /^(0[1-7]|1[0-356]|2[0-7]|3[0-6]|4[0-7]|5[1-7]|6[1-7]|7[0-5]|8[013-6])\d{4}$/,
                        message: "请正确填写邮编",
                    },
                },
                label: { value: "邮编", required: true },
            },
        },
        {
            type: "input",
            name: "tel",
            attribute: {
                placeholder: "请填写联系电话",
                rule: {
                    required: "联系电话不能为空",
                    pattern: {
                        value: /^(?:(?:\d{3}-)?\d{8}|^(?:\d{4}-)?\d{7,8})(?:-\d+)?$|^(0|86|17951)?(1[0-9])[0-9]{9}$/,
                        message: "请正确填写联系电话，手机或座机",
                    },
                },
                label: { value: "联系电话", required: true },
            },
        },
        {
            type: "input",
            name: "email",
            attribute: {
                placeholder: "请填写邮箱",
                rule: {
                    required: "邮箱不能为空",
                    pattern: {
                        value: /\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*/,
                        message: "请正确填写邮箱",
                    },
                },
                label: { value: "邮箱", required: true },
            },
        },
        {
            type: "input",
            name: "corporate",
            attribute: {
                placeholder: "请填写法人",
                rule: {
                    required: "法人不能为空",
                    maxLength: {
                        value: 100,
                        message: "法人长度不能超过100个字符",
                    },
                },
                label: { value: "法人", required: true },
            },
        },
        {
            type: "input",
            name: "licenseCode",
            attribute: {
                placeholder: "请填写营业执照编码",
                rule: {
                    required: "营业执照编码不能为空",
                    maxLength: {
                        value: 100,
                        message: "营业执照编码不能超过100个字符",
                    },
                },
                label: { value: "营业执照编码", required: true },
            },
        },
    ];
    // 创建 vender 用户
    const creatVenderFun = async (data) => {
        const userRes = await creatVender(data);
        console.log("userRes", userRes);
        if (userRes && userRes.id) {
            setCreatVenderState(false);
            // vender 用户创建成功
            setUserInfo(userRes);
            // 创建完成跳转主页
            props.history.push(referrer);
        } else {
            message.error(`商户信息建立失败！${userRes.message}`);
        }
    };

    //登出清空信息
    const signOut = useCallback(() => {
        localStorage.removeItem("hc-apm-token");
        localStorage.removeItem("venderInfo");
        setUserInfo(null);
        setOpenId(null);
        setCreatVenderState(false);
    }, []);

    const onSuccess = (res) => {
        setOpenId(res.openid);
        localStorage.setItem("hc-apm-token", res.authorization);
        console.log("res==", res);

        if (res.venderInfo && res.venderInfo.venderCompanyId) {
            //已注册的 vender 用户
            setUserInfo(res.venderInfo);
            props.history.push(referrer);
        } else {
            if (res.invitation) {
                //已注册的 vender 用户
                setUserInfo({
                    ...res.venderCompany,
                    invitation: res.invitation,
                });
                // 添加 vender 用户
                setCreatVenderState(true);
                setCreatVenderType("add");
            } else {
                // 新创建 vender 用户
                setCreatVenderState(true);
                setCreatVenderType("creat");
            }
        }
    };
    return (
        <div className="vender-login">
            <h3>GEHC APM 供应商管理</h3>
            <div className="login-wrap">
                {/* <MyForm
                    options={loginForm}
                    onSubmit={(data) => {
                        console.log("data", data);
                        // creatVenderFun(data);
                    }}
                    onSubmitText="登录"
                ></MyForm>
                <span></span> */}
                <div className="qrcode-wrap">
                    <h4>扫码登录</h4>
                    <QrCodeLogin
                        onSuccess={onSuccess}
                        name={"Supplier"}
                        venderCompanyId={query.venderCompanyId}
                    />
                </div>
            </div>
            <Modal
                closable={false}
                centered
                title={creatVenderType == "creat" ? "新建账户" : "添加账户"}
                open={true}
                footer={null}
                keyboard={false}
                maskClosable={false}
                getContainer={() =>
                    document.getElementsByClassName("vender-login")[0]
                }
                width="450"
            >
                <MyForm
                    data={{
                        bidType: "Vender",
                        openId,
                        ...model,
                        ...model.venderCompany,
                    }}
                    options={creatVenderForm}
                    oncancel={() => {
                        creatVenderType == "creat"
                            ? signOut()
                            : setCreatVenderState(false);
                    }}
                    onSubmit={(data) => {
                        console.log("data", data);
                        const {
                            openId,
                            bidType,
                            venderName,
                            venderPhone,
                            name,
                            city,
                            address,
                            zipcode,
                            tel,
                            email,
                            corporate,
                            licenseCode,
                            invitation,
                            id,
                        } = data;
                        let newObj = {
                            bidType,
                            venderName,
                            venderPhone,
                            venderCompanyId: id,
                            openId,
                            invitation,
                            venderCompany: {
                                name,
                                city,
                                address,
                                zipcode,
                                tel,
                                email,
                                corporate,
                                licenseCode,
                            },
                        };
                        console.log("newObj", newObj);
                        creatVenderFun(newObj);
                    }}
                ></MyForm>
            </Modal>
        </div>
    );
};

export default VenderLogin;
