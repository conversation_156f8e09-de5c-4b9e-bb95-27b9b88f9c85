.RadQCBoard {
    background: url(../../images/Dashboard_bg.png) no-repeat center 0px #141414;
    width: 1920px;

    .ant-layout-header {
        position: sticky;
        top: 0;
        background: #313235;
        padding: 0 10px;
        display: flex;
        justify-content: center;
        position: relative;
        align-items: center;
        color: #fff;

        .ge-logo {
            width: 150px;
            position: absolute;
            left: 10px;
        }

        .header-title {
            font-size: 26px;
            display: flex;
            align-items: center;
        }

        .CT-icon {
            width: 32px;
            margin-right: 10px;
        }
    }

    .ant-layout-content {
        padding: 0 16px;
    }

    .time-interval {
        display: flex;
        justify-content: center;
        padding: 20px 0;

        .ant-radio-group {
            border: 1px solid #878C92;
            padding: 2px 0;
            border-radius: 4px;
            background: #3D3F42;

            .ant-radio-button-wrapper {
                width: 80px;
                padding: 0;
                margin: 0 2px;
                text-align: center;
                line-height: 32px;
                background: #3D3F42;
                color: #F9F9FA;
                border: none;
                border-radius: 3px;

                &:not(:first-child)::before {
                    top: 10%;
                    left: -2px;
                    height: 80%;
                    background-color: #878C92;
                }
            }

        }

        .ant-radio-group-solid {
            .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
                color: #333;
                background: #A9A0C5;
                border-color: #A9A0C5;
            }
        }



    }

    .year-QC-board {
        border-radius: 8px;
        background: url(../../images/ct.png) no-repeat 30px 80px rgba(30, 31, 32, .6);
        padding: 10px;
        height: 290px;

        h3 {
            margin-bottom: 15px;
            color: #eee;
            font-size: 18px;
            font-weight: normal;
        }

        .QC-chart {
            display: flex;
            padding-left: 220px;

            .QC-chart-item {
                height: 220px;
                margin-left: 10px;
                padding: 10px;
                border: 1px solid #55595e;
                color: #f9f9fa;

                h4 {
                    color: #f9f9fa;
                    font-size: 16px;
                    font-weight: normal;
                }
            }




            .order-num,
            .device-num {
                width: 580px;

                .order-cont,
                .device-cont {
                    display: flex;

                    .order-num-wrap,
                    .device-num-wrap {
                        flex: 1 1 100px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        flex-direction: column;
                        padding: 0 10px 0 0;

                        p {
                            font-size: 70px;
                            margin-bottom: 5px;
                            line-height: 1;
                            font-weight: 300;
                        }

                        span {
                            font-size: 16px;
                        }
                    }

                    .order-progress-wrap,
                    .device-progress-wrap {
                        flex: 0 1 120px;
                        margin: 0 20px;

                        .progress-wrap {
                            margin: 10px auto 5px;
                            display: flex;
                            justify-content: space-between;
                            align-items: baseline;
                        }

                        .progress-type {
                            color: #babdc0;
                            line-height: 1;
                        }

                        .progress-down,
                        .progress-up {
                            display: inline-block;
                            width: 0;
                            height: 0;
                            border: 10px solid transparent;
                            border-color: transparent transparent #8fbf9d transparent;
                            border-width: 0 8px 16px 8px;
                        }

                        .progress-down {
                            border-color: #a32940 transparent transparent transparent;
                            border-width: 16px 8px 0 8px;
                        }

                        .progress-value {
                            line-height: 1;
                        }

                        .ant-progress {
                            display: flex;
                            justify-content: center;
                            align-items: center;
                        }

                        .ant-progress-text {
                            font-size: 16px;
                            color: #F9F9FA;
                            font-weight: 200;
                            text-align: right;
                        }
                    }

                    .order-echart,
                    .device-echart {
                        flex: 1 1 200px;

                    }
                }
            }

            .device-num {
                .device-cont .device-echart {
                    flex: 1 1 200px;

                    .echarts-for-react {
                        height: 150px !important
                    }
                }
            }

            .order-num {
                .order-cont .order-echart {
                    flex: 1 1 200px;

                    .echarts-for-react {
                        height: 150px !important
                    }
                }
            }

            .pass-rate {
                width: 220px;

                .ant-progress {
                    width: 100%;
                    height: calc(100% - 20px);
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                }

                .ant-progress-text {
                    font-size: 40px;
                    color: #F9F9FA;
                    font-weight: 200;

                    .percent-sign {
                        font-size: 20px;
                    }
                }


            }

            .startup-rate {
                width: 220px;

                .echart-wrap {
                    width: 100%;
                    height: calc(100% - 20px);
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                }

                .echarts-for-react {
                    width: 100%;
                    height: 180px !important;
                }
            }
        }
    }

    .QC-data-section {
        display: flex;
        margin: 16px auto;
        color: #f9f9fa;

        .QC-data-loop,
        .QC-system-section,
        .QC-empty-section {
            height: 629px;
            width: 739px;
            border-radius: 8px;
            background: rgba(30, 31, 32, .6);
            padding: 16px;

            h3 {
                color: #f9f9fa;
                font-size: 18px;
                font-weight: normal;
            }
        }

        // 质控闭环数据（工单维度）
        .QC-data-loop {
            .QC-data-content {
                border: 1px solid #55595e;
                height: calc(100% - 34px);
                padding: 16px;
            }

            .QC-data-echart {
                height: 150px;
                margin-bottom: 16px;

                .echarts-for-react {
                    height: 150px !important;
                }
            }

            .QC-data-progress {
                display: flex;
                align-items: start;

                .progress-item {
                    flex: 0 1 40%;
                }

                .item-content {
                    margin-bottom: 28px;

                    h5 {
                        margin-bottom: 4px;
                        color: #F9F9FA;
                        font-size: 16px;
                        font-weight: normal;
                    }

                    .ant-progress-outer {
                        width: calc(100% - 100px);
                    }

                    .ant-progress-text {
                        margin-left: 30px;
                        font-size: 40px;
                        color: #F9F9FA;
                    }
                }

                .QC-data-cycle {
                    flex: 0 1 60%;
                    margin-top: 10px;
                }
            }
        }

        // 医疗设备质量控制体系
        .QC-system-section {
            height: 629px;
            width: 816px;
            margin-left: 16px;
        }

        .QC-empty-section {
            width: 301px;
            margin-left: 16px;
            border: none;

            p {
                font-size: '16px';
                line-height: '24px'
            }

            .item-ad {
                height: 90px;
                margin-bottom: 10px;
                padding: 16px;
                border-radius: 12px;
                background-color: #313235;
                display: flex;
                align-items: center;
                justify-content: flex-start;

                .item-icon {
                    width: 60px;
                    height: 60px;
                    margin-right: 20px;
                    text-align: center;
                }

                img {
                    height: 60px;
                }
            }
        }
    }
}

// 动画关键帧
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}