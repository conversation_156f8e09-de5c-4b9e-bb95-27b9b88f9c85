import React, { useMemo, useRef } from "react";
import { Tooltip } from "antd";
import ReactEcharts from "echarts-for-react";
import { toDecimal } from "@/urls/utils";
const color = [
    "#7252BC",
    "#A918F4",
    "#4A66E6",
    "#2B9CDB",
    "#81CFFA",
    "#5AEA80",
    "#90DB73",
    "#92278f",
    "#755dd9",
    "#45a5ed",
    "#581756",
    "#3a2397",
    "#1067a7",
    "#3ba272",
];

function DataEchart(props) {
    const echartDom = useRef(null);
    const { data, name } = props;

    const echartOption = useMemo(() => {
        if (!data || Array.isArray(data) === false || data.length === 0) {
            return {};
        }

        switch (name) {
            case "device-num":
                data.sort((a, b) => b.numerical - a.numerical);
                return {
                    color,
                    tooltip: {
                        trigger: "item",
                        confine: true,
                        formatter: "{b}:{c}",
                        textStyle: {
                            fontSize: "12",
                        },
                    },
                    legend: {
                        orient: "vertical",
                        type: "scroll",
                        left: "50%",
                        top: 15,
                        bottom: 20,
                        textStyle: {
                            fontSize: "12px",
                            color: "#fff",
                        },
                        itemWidth: 9,
                        itemHeight: 9,
                        icon: "circle",
                        pageIconSize: [8, 8],
                        formatter: function (data) {
                            const toJsonData = JSON.parse(data);

                            return toJsonData.name.length > 8
                                ? toJsonData.name.slice(0, 8) +
                                      "..." +
                                      "  " +
                                      toJsonData.value
                                : toJsonData.name + "  " + toJsonData.value;
                        },
                    },
                    series: [
                        {
                            type: "pie",
                            minAngle: 10,
                            radius: ["40%", "65%"],
                            center: ["24%", "50%"],
                            roseType: "area",
                            avoidLabelOverlap: false,
                            itemStyle: {
                                borderRadius: 0,
                            },
                            label: {
                                show: false,
                            },
                            emphasis: {
                                label: {
                                    show: false,
                                },
                            },
                            data: data.map((item) => ({
                                value: item.numerical,
                                name: `{"name": "${item.name}","value": ${item.numerical}}`,
                                percent: item.numerical,
                            })),
                        },
                    ],
                };

            case "order-num":
                let filteredWOData = data.filter((item) => item.numerical > 0);
                let sortedWOData = filteredWOData.sort(
                    (a, b) => b.numerical - a.numerical
                );
                return {
                    color,
                    tooltip: {
                        trigger: "axis",
                        axisPointer: {
                            type: "shadow",
                        },
                        textStyle: {
                            fontSize: "12",
                        },
                        confine: true,
                        formatter: "{b}<br/>{a}: {c}",
                    },
                    grid: {
                        show: true,
                        top: "10%",
                        left: "3%",
                        right: "2%",
                        bottom: "15%",
                        borderWidth: 1,
                        borderColor: "#55595e",
                    },
                    xAxis: {
                        type: "category",
                        data: sortedWOData.map((item) => item.name),
                        axisLabel: {
                            interval: 0,
                            show: true,
                        },
                        axisTick: {
                            show: false,
                        },
                    },
                    yAxis: {
                        type: "value",
                        max: 500,
                        position: "left",
                        minInterval: 1,
                        axisLabel: {
                            show: false,
                        },
                        splitLine: {
                            lineStyle: {
                                // 使用深浅的间隔色
                                color: ["#55595e"],
                                type: "dashed",
                            },
                        },
                    },
                    color: "#7890f3",
                    series: [
                        {
                            name: "质控完成工单量",
                            type: "bar",
                            barWidth: 16,
                            data: sortedWOData.map((item) => item.numerical),
                            itemStyle: {
                                borderRadius: [16, 16, 0, 0],
                            },
                            label: {
                                show: true,
                                position: "top",
                                fontSize: "14",
                                color: "#fff",
                                formatter: (params) => {
                                    return params.value;
                                },
                            },
                        },
                    ],
                };

            case "qc-order":
                let filteredqcData = data.filter((item) => item.numerical > 0);
                return {
                    color,
                    tooltip: {
                        trigger: "axis",
                        axisPointer: {
                            type: "shadow",
                        },
                        textStyle: {
                            fontSize: "12",
                        },
                        confine: true,
                        formatter: "{b}<br/>{a}: {c}%",
                    },
                    grid: {
                        show: true,
                        top: "10%",
                        left: "10%",
                        right: "5%",
                        bottom: "15%",
                        borderWidth: 1,
                        borderColor: "#55595e",
                    },
                    xAxis: {
                        type: "value",
                        name: "%",
                        max: 100,
                        position: "left",
                        minInterval: 1,
                        axisLabel: {
                            show: true,
                        },
                        splitLine: {
                            lineStyle: {
                                // 使用深浅的间隔色
                                color: ["#55595e"],
                                type: "dashed",
                            },
                        },
                    },
                    yAxis: {
                        type: "category",
                        data: filteredqcData.map((item) => item.name),
                        axisLabel: {
                            interval: 0,
                            show: true,
                        },
                        axisTick: {
                            show: true,
                        },
                    },
                    color: "#7890f3",
                    series: [
                        {
                            name: "质控完成工单量",
                            type: "bar",
                            barWidth: 8,
                            data: filteredqcData.map((item) => item.numerical),
                            itemStyle: {
                                borderRadius: [0, 16, 16, 0],
                            },
                            label: {
                                show: true,
                                position: "right",
                                fontSize: "14",
                                color: "#fff",
                                formatter: (params) => {
                                    return params.value;
                                },
                            },
                        },
                    ],
                };

            default:
                return {};
        }
    }, [data]);

    return (
        <ReactEcharts
            ref={echartDom}
            className="echart-canvas"
            option={echartOption}
            notMerge
        />
    );
}

export default DataEchart;
