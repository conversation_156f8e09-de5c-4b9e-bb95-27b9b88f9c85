import React from "react";
import { rate_bg, pointer } from "@/images/export";
import "./index.less";

function Rate(props) {
    const { value = 0 } = props;

    // 初始角度58，最大角度300，0-100%四等份
    // 0%: 58deg, 100%: 300deg
    // 每1%: (300-58)/100 = 2.42deg
    const minAngle = 58;
    const maxAngle = 300;
    const rotate =
        value >= 0 && value <= 100
            ? minAngle + (maxAngle - minAngle) * (value / 100)
            : minAngle;
    const pointerStyle = {
        transform: `rotate(${rotate}deg)`,
        transformOrigin: "right 0",
        transition: "transform 0.3s ease",
    };

    return (
        <div className="rate-wrap">
            <img className="rate-bg" src={rate_bg} />
            <div className="rate-r">
                <span style={pointerStyle} className="rate-pointer"></span>
            </div>
        </div>
    );
}

export default Rate;
