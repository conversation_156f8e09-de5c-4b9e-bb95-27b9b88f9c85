.rate-wrap {
    position: relative;

    .rate-r {
        position: absolute;
        top: 50%;
        left: 50%;
        width: 10px;
        height: 10px;
        transform: translate(-7px, 16px);

        &::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 10px;
            height: 10px;
            border-radius: 100%;
            background: #878c92;
            z-index: 10;
        }

        .rate-pointer {
            position: absolute;
            top: 5px;
            left: 4px;
            width: 1px;
            height: 40px;
            background: #f9f9fa;
        }
    }


}

.qc-system-carousel {
    height: calc(100% - 19px);



    .carousel-content {
        height: 100%;

        .carousel-header {
            height: 200px;
            margin-bottom: 16px;
            position: relative;

            .carousel-indicators {
                display: flex;
                justify-content: center;
                position: absolute;
                bottom: 0;
                width: 100%;

                .indicator {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    cursor: pointer;
                    position: relative;
                    width: 25%;

                    .indicator-number {
                        width: 32px;
                        height: 32px;
                        border-radius: 50%;
                        border: 1px solid #878c92;
                        background: #3d3f42;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        color: #f9f9fa;
                        font-size: 14px;
                        font-weight: bold;
                        margin-bottom: 8px;
                        transition: all 0.3s ease;
                    }



                    &.active {
                        .indicator-number {
                            background: linear-gradient(135deg, #a9a0c5 10%, #a9a0c5 100%);
                            color: #fff;
                            border-color: #a9a0c5;
                            transform: scale(1.08);
                        }

                        .indicator-progress {
                            background: rgba(249, 249, 250, 0.4);
                        }
                    }

                    &:hover {
                        .indicator-number {
                            border-color: #a9a0c5;
                            transform: scale(1.05);
                        }
                    }
                }

                .indicator-progress {
                    position: absolute;
                    top: 15px;
                    width: 100%;
                    display: flex;
                    padding: 0 130px;
                    justify-content: space-between;

                    .progress-bar-wrap {
                        width: 130px;
                        height: 2px;
                        background: rgba(249, 249, 250, 0.2);
                        border-radius: 2px;
                        overflow: hidden;
                    }

                    .progress-bar {
                        height: 100%;
                        background: linear-gradient(90deg, #7245D9 0%, #9670EE 100%);
                        border-radius: 2px;
                        transition: width 0.1s ease;
                    }
                }
            }

            .header-icons {
                display: flex;
                margin-bottom: 20px;
                justify-content: space-between;

                .item-wrap-first,
                .item-wrap-last {
                    flex: 0 1 566px;
                    display: flex;
                    border-radius: 10px;
                    background: #313235;
                    height: 219px;

                }

                .item-wrap-last {
                    flex: 0 1 208px;
                }

                .icon-item {
                    width: 80px;
                    height: 80px;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    flex: 1;
                    margin: 30px auto 0;

                    img {
                        height: 80px;
                    }

                    .icon-labels {
                        text-align: center;

                        .icon-label {
                            display: block;
                            color: #F9F9FA;
                            font-size: 14px;
                            margin-bottom: 4px;
                        }

                        .icon-sublabel {
                            display: block;
                            color: #BABDC0;
                            font-size: 12px;
                        }
                    }
                }
            }


        }

        .carousel-body {
            height: calc(100% - 278px);
            border: 1px solid #55595e;
            border-radius: 8px;
            padding: 16px;
            background: rgba(49, 50, 53, 0.3);
            backdrop-filter: blur(10px);

            .state-content {
                height: 100%;
                opacity: 0;
                animation: fadeIn 0.5s ease-in-out forwards;

                &.state-daily {}

                &.state-monthly {
                    .monthly-header {
                        margin-bottom: 16px;

                        .section-title {
                            color: #F9F9FA;
                            font-size: 16px;
                            font-weight: bold;
                        }
                    }


                }

                &.state-repair {
                    display: flex;
                    gap: 20px;

                    .repair-stats {
                        flex: 0 0 200px;
                        display: flex;
                        flex-direction: column;
                        gap: 20px;

                        .stat-circle {
                            display: flex;
                            flex-direction: column;
                            align-items: center;

                            .circle-content {
                                width: 80px;
                                height: 80px;
                                border-radius: 50%;
                                border: 2px solid #7245D9;
                                display: flex;
                                flex-direction: column;
                                align-items: center;
                                justify-content: center;
                                margin-bottom: 8px;

                                .stat-number {
                                    color: #F9F9FA;
                                    font-size: 24px;
                                    font-weight: bold;
                                    line-height: 1;
                                }

                                .stat-unit {
                                    color: #BABDC0;
                                    font-size: 12px;
                                }
                            }

                            .stat-label {
                                color: #F9F9FA;
                                font-size: 14px;
                            }
                        }
                    }

                    .repair-progress {
                        flex: 1;

                        .progress-header {
                            margin-bottom: 16px;

                            span {
                                color: #F9F9FA;
                                font-size: 16px;
                                font-weight: bold;
                            }
                        }

                        .progress-list {
                            .repair-item {
                                margin-bottom: 16px;

                                .repair-name {
                                    color: #F9F9FA;
                                    font-size: 14px;
                                    margin-bottom: 8px;
                                }

                                .repair-bar {
                                    height: 8px;
                                    background: rgba(249, 249, 250, 0.1);
                                    border-radius: 4px;
                                    overflow: hidden;

                                    .repair-progress-fill {
                                        height: 100%;
                                        background: linear-gradient(90deg, #00C2EB 0%, #7245D9 100%);
                                        border-radius: 4px;
                                        transition: width 0.3s ease;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

}