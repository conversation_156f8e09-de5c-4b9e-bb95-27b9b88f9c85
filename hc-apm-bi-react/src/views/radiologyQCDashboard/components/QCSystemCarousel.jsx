import React, { useState, useEffect, useRef } from "react";
import { icon_1, icon_2, icon_3, icon_4 } from "@/images/export";
import "./index.less";

const QCSystemCarousel = () => {
    const [currentState, setCurrentState] = useState(0);
    const [progress, setProgress] = useState(0);
    const [animationKey, setAnimationKey] = useState(0);
    const interval = useRef();
    const progressInterval = useRef();

    // 三种状态的数据配置
    const statesData = [
        {
            title: "医疗设备质量控制体系",
            subtitle: "临床 | 日常质控",
            contentData: [
                { name: "日常质控", value: 50 },
                { name: "月度质控", value: 12 },
                { name: "保养", value: 98 },
            ],
        },
        {
            title: "医疗设备质量控制体系",
            subtitle: "临床 | 日常质控",
            contentData: [
                { name: "日常质控", value: 50 },
                { name: "月度质控", value: 12 },
                { name: "保养", value: 98 },
            ],
        },
        {
            title: "医疗设备质量控制体系",
            subtitle: "医工 | 设备维修",
            contentData: [
                { name: "报修", count: 99, unit: "台" },
                { name: "维修", count: 99, unit: "台" },
            ],
            repairData: [
                { name: "急诊1楼各类数字化摄影系统质控检测", progress: 60 },
                { name: "门诊1楼数字化摄影系统质控检测", progress: 45 },
                { name: "放射科1号室数字化摄影系统质控检测", progress: 30 },
                { name: "放射科3号室数字化摄影系统质控检测", progress: 85 },
            ],
        },
        {
            title: "医疗设备质量控制体系",
            subtitle: "医工 | 月度质控&保养",
            contentData: [
                {
                    name: "年度质控",
                    value: 2023,
                    date: "2023年11月20日",
                    status: "合格",
                    nextDate: "2024年11月",
                },
                {
                    name: "年度质控",
                    value: 2023,
                    date: "2023年11月20日",
                    status: "合格",
                    nextDate: "2024年11月",
                },
                {
                    name: "年度质控",
                    value: 2023,
                    date: "2023年11月20日",
                    status: "合格",
                    nextDate: "2024年11月",
                },
                {
                    name: "年度质控",
                    value: 2023,
                    date: "2023年11月20日",
                    status: "合格",
                    nextDate: "2024年11月",
                },
                {
                    name: "年度质控",
                    value: 2023,
                    date: "2023年11月20日",
                    status: "合格",
                    nextDate: "2024年11月",
                },
            ],
        },
    ];

    // 自动轮播逻辑
    useEffect(() => {
        interval.current = setInterval(() => {
            setCurrentState((prev) => (prev + 1) % statesData.length);
            setProgress(0); // 重置进度条
            setAnimationKey((prev) => prev + 1); // 重置动画
        }, 5000);

        return () => clearInterval(interval.current);
    }, [statesData.length]);

    // 进度条动画
    useEffect(() => {
        progressInterval.current = setInterval(() => {
            setProgress((prev) => {
                if (prev >= 100) {
                    return 0;
                }
                return prev + 2; // 每100ms增加2%，5秒完成
            });
        }, 100);

        return () => clearInterval(progressInterval.current);
    }, [currentState]);

    const currentData = statesData[currentState];

    return (
        <div className="qc-system-carousel">
            {/* 轮播内容 */}
            <div className="carousel-content">
                {/* 上半部分 - 进度展示 */}
                <div className="carousel-header">
                    <div className="header-icons">
                        <div className="item-wrap-first">
                            <div className="icon-item">
                                <img src={icon_1} alt={`icon-1`} />
                            </div>
                            <div className="icon-item">
                                <img src={icon_2} alt={`icon-2`} />
                            </div>
                            <div className="icon-item">
                                <img src={icon_3} alt={`icon-3`} />
                            </div>
                        </div>
                        <div className="item-wrap-last">
                            <div className="icon-item">
                                <img src={icon_4} alt={`icon-4`} />
                            </div>
                        </div>
                    </div>
                    {/* 进度指示器 */}
                    <div className="carousel-indicators">
                        {statesData.map((item, index) => (
                            <div
                                key={index}
                                className={`indicator ${
                                    index <= currentState ? "active" : ""
                                }`}
                                onClick={() => {
                                    setCurrentState(index);
                                    setProgress(0);
                                    setAnimationKey((prev) => prev + 1);
                                    clearInterval(interval.current);
                                    clearInterval(progressInterval.current);
                                }}
                            >
                                <div className="indicator-number">
                                    {index + 1}
                                </div>

                                <span>{item.subtitle}</span>
                            </div>
                        ))}
                        <div className="indicator-progress">
                            {statesData
                                .slice(0, statesData.length - 1)
                                .map((_, index) => (
                                    <div
                                        key={index}
                                        className="progress-bar-wrap"
                                    >
                                        {index <= currentState && (
                                            <div
                                                className="progress-bar"
                                                style={{
                                                    width:
                                                        index >= currentState
                                                            ? `${progress}%`
                                                            : "100%",
                                                }}
                                            />
                                        )}
                                    </div>
                                ))}
                        </div>
                    </div>
                </div>

                {/* 下半部分 - 内容变化 */}
                <div className="carousel-body">
                    {currentState === 0 && (
                        <div
                            key={`daily-${animationKey}`}
                            className="state-content state-daily"
                        >
                            <div className="content-table">content-table</div>
                        </div>
                    )}

                    {currentState === 1 && (
                        <div
                            key={`monthly-${animationKey}`}
                            className="state-content state-monthly"
                        >
                            <div className="monthly-header">
                                <span className="section-title">
                                    年度质控工单明细
                                </span>
                            </div>
                            <div className="monthly-table">monthly-table</div>
                        </div>
                    )}

                    {currentState === 2 && (
                        <div
                            key={`repair-${animationKey}`}
                            className="state-content state-repair"
                        >
                            <div className="repair-stats">
                                {currentData.contentData.map((item, index) => (
                                    <div key={index} className="stat-circle">
                                        <div className="circle-content">
                                            <div className="stat-number">
                                                {item.count}
                                            </div>
                                            <div className="stat-unit">
                                                {item.unit}
                                            </div>
                                        </div>
                                        <div className="stat-label">
                                            {item.name}
                                        </div>
                                    </div>
                                ))}
                            </div>
                            <div className="repair-progress">
                                <div className="progress-header">
                                    <span>高故障设备名</span>
                                </div>
                                <div className="progress-list">
                                    {currentData.repairData.map(
                                        (item, index) => (
                                            <div
                                                key={index}
                                                className="repair-item"
                                            >
                                                <div className="repair-name">
                                                    {item.name}
                                                </div>
                                                <div className="repair-bar">
                                                    <div
                                                        className="repair-progress-fill"
                                                        style={{
                                                            width: `${item.progress}%`,
                                                        }}
                                                    />
                                                </div>
                                            </div>
                                        )
                                    )}
                                </div>
                            </div>
                        </div>
                    )}
                    {currentState === 3 && (
                        <div
                            key={`daily-${animationKey}`}
                            className="state-content state-daily"
                        >
                            <div className="content-table">content-table-4</div>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default QCSystemCarousel;
