import React, { useState, useEffect } from "react";
import { Layout, Radio, Progress } from "antd";
import "./index.less";
import {
    GEHCLogo_h_s,
    CT_icon,
    cycle_bg,
    icon_1,
    icon_2,
    icon_3,
    icon_4,
    icon_5,
    icon_6,
    icon_7,
    icon_8,
    icon_9,
} from "@/images/export";
import DataEchart from "./components/DataEchart.jsx";
import Rate from "./components/Rate.jsx";
import QCSystemCarousel from "./components/QCSystemCarousel.jsx";
import moment from "moment";
const { Header, Content } = Layout;
const filterTimeOptions = [
    { label: "全部", value: "year" },
    { label: "近90天", value: "quarter" },
    { label: "近30天", value: "month" },
    { label: "近七天", value: "week" },
];

function RadQCBoard() {
    const { selectTimeScope, radioValue, filterTime } = useRadQCBoard();
    return (
        <Layout className="RadQCBoard">
            <Header>
                <img className="ge-logo" src={GEHCLogo_h_s} alt="ge-logo" />
                <span className="header-title">
                    <img className="CT-icon" src={CT_icon} alt="ge-logo" />
                    放射科质控大屏
                </span>
            </Header>
            <Content>
                <div className="time-interval">
                    <Radio.Group
                        buttonStyle="solid"
                        options={filterTimeOptions}
                        optionType="button"
                        onChange={(e) => {
                            selectTimeScope(e.target.value);
                        }}
                        value={radioValue}
                    />
                </div>
                <div className="year-QC-board">
                    <h3 className="year-QC-board-title">当年质控概览</h3>
                    <div className="QC-chart">
                        <div className="QC-chart-item device-num">
                            <h4>质控设备数量</h4>
                            <div className="device-cont">
                                <div className="device-num-wrap">
                                    <p>28</p>
                                    <span>台</span>
                                </div>
                                <div className="device-progress-wrap">
                                    <div className="progress-wrap">
                                        <span className="progress-type">
                                            同比
                                        </span>
                                        <i className="progress-up"></i>
                                        <span className="progress-value">
                                            8%
                                        </span>
                                    </div>

                                    <Progress
                                        percent={12}
                                        strokeColor={{
                                            from: "#7245D9",
                                            to: "#9670EE",
                                        }}
                                        strokeWidth="4px"
                                        trailColor="rgba(249, 249, 250, 0.08)"
                                        format={(value) => {
                                            return value;
                                        }}
                                    />

                                    <Progress
                                        percent={13.5}
                                        strokeColor={{
                                            from: "#878C92",
                                            to: "#878C92",
                                        }}
                                        strokeWidth="4px"
                                        trailColor="rgba(249, 249, 250, 0.08)"
                                        format={(value) => {
                                            return value;
                                        }}
                                    />

                                    <div className="progress-wrap">
                                        <span className="progress-type">
                                            同比
                                        </span>{" "}
                                        <i className="progress-down"></i>
                                        <span className="progress-value">
                                            8%
                                        </span>
                                    </div>

                                    <Progress
                                        percent={12}
                                        strokeColor={{
                                            from: "#7245D9",
                                            to: "#9670EE",
                                        }}
                                        strokeWidth="4px"
                                        trailColor="rgba(249, 249, 250, 0.08)"
                                        format={(value) => {
                                            return value;
                                        }}
                                    />
                                    <Progress
                                        percent={13.5}
                                        strokeColor={{
                                            from: "#878C92",
                                            to: "#878C92",
                                        }}
                                        strokeWidth="4px"
                                        trailColor="rgba(249, 249, 250, 0.08)"
                                        format={(value) => {
                                            return value;
                                        }}
                                    />
                                </div>
                                <div className="device-echart">
                                    <DataEchart
                                        name={"device-num"}
                                        data={[
                                            { name: "CT", numerical: 10 },
                                            { name: "DR", numerical: 10 },
                                            { name: "MRI", numerical: 10 },
                                            { name: "DSA", numerical: 10 },
                                            { name: "PET", numerical: 10 },
                                            { name: "超声", numerical: 10 },
                                            { name: "乳腺", numerical: 10 },
                                        ]}
                                    ></DataEchart>
                                </div>
                            </div>
                        </div>
                        <div className="QC-chart-item order-num">
                            <h4>质控完成工单量</h4>
                            <div className="order-cont">
                                <div className="order-num-wrap">
                                    <p>1400</p>
                                    <span>单</span>
                                </div>
                                <div className="order-progress-wrap">
                                    <div className="progress-wrap">
                                        <span className="progress-type">
                                            同比
                                        </span>
                                        <i className="progress-up"></i>
                                        <span className="progress-value">
                                            8%
                                        </span>
                                    </div>
                                    <Progress
                                        percent={12}
                                        strokeColor={{
                                            from: "#7245D9",
                                            to: "#9670EE",
                                        }}
                                        strokeWidth="4px"
                                        trailColor="rgba(249, 249, 250, 0.08)"
                                        format={(value) => {
                                            return value;
                                        }}
                                    />

                                    <Progress
                                        percent={13.5}
                                        strokeColor={{
                                            from: "#878C92",
                                            to: "#878C92",
                                        }}
                                        strokeWidth="4px"
                                        trailColor="rgba(249, 249, 250, 0.08)"
                                        format={(value) => {
                                            return value;
                                        }}
                                    />
                                    <div className="progress-wrap">
                                        <span className="progress-type">
                                            同比
                                        </span>{" "}
                                        <i className="progress-down"></i>
                                        <span className="progress-value">
                                            8%
                                        </span>
                                    </div>
                                    <Progress
                                        percent={12}
                                        strokeColor={{
                                            from: "#7245D9",
                                            to: "#9670EE",
                                        }}
                                        strokeWidth="4px"
                                        trailColor="rgba(249, 249, 250, 0.08)"
                                        format={(value) => {
                                            return value;
                                        }}
                                    />

                                    <Progress
                                        percent={13.5}
                                        strokeColor={{
                                            from: "#878C92",
                                            to: "#878C92",
                                        }}
                                        strokeWidth="4px"
                                        trailColor="rgba(249, 249, 250, 0.08)"
                                        format={(value) => {
                                            return value;
                                        }}
                                    />
                                </div>
                                <div className="order-echart">
                                    <DataEchart
                                        name={"order-num"}
                                        data={[
                                            {
                                                name: "日常质控",
                                                numerical: 364,
                                            },
                                            { name: "月度质控", numerical: 12 },
                                            { name: "保养", numerical: 4 },
                                        ]}
                                    ></DataEchart>
                                </div>
                            </div>
                        </div>
                        <div className="QC-chart-item pass-rate">
                            <h4>强检合格率</h4>

                            <Progress
                                format={(value) => (
                                    <div className="text-wrap">
                                        <div>{value}</div>
                                        <div className="percent-sign">%</div>
                                    </div>
                                )}
                                strokeColor="#8FBF9D"
                                strokeWidth="5"
                                trailColor="rgba(249, 249, 250, 0.06)"
                                type="circle"
                                percent={75}
                            />
                        </div>
                        <div className="QC-chart-item startup-rate">
                            <h4>开机率 (%)</h4>
                            <div className="echart-wrap">
                                <Rate value={30} />
                            </div>
                        </div>
                    </div>
                </div>
                <div className="QC-data-section">
                    <div className="QC-data-loop">
                        <h3 className="QC-data-title">
                            质控闭环数据（工单维度）
                        </h3>
                        <div className="QC-data-content">
                            <div className="QC-data-echart">
                                <DataEchart
                                    name={"qc-order"}
                                    data={[
                                        {
                                            name: "质控完成率",
                                            numerical: 100,
                                        },
                                        {
                                            name: "初检合格率",
                                            numerical: 96.92,
                                        },
                                        { name: "复检合格率", numerical: 100 },
                                    ]}
                                ></DataEchart>
                            </div>
                            <div className="QC-data-progress">
                                <div className="progress-item">
                                    <div className="item-content">
                                        <h5>初检合格量（台）</h5>
                                        <Progress
                                            percent={58}
                                            strokeColor={{
                                                from: "#7245D9",
                                                to: "#9670EE",
                                            }}
                                            strokeWidth="8px"
                                            trailColor="rgba(249, 249, 250, 0.08)"
                                            format={(value) => {
                                                return value;
                                            }}
                                        />
                                    </div>
                                    <div className="item-content">
                                        <h5>现场维修量（台）</h5>
                                        <Progress
                                            percent={19}
                                            strokeColor={{
                                                from: "#00C2EB",
                                                to: "#00C2EB",
                                            }}
                                            strokeWidth="8px"
                                            trailColor="rgba(249, 249, 250, 0.08)"
                                            format={(value) => {
                                                return value;
                                            }}
                                        />
                                    </div>
                                    <div className="item-content">
                                        <h5>复检合格量/不合格量（台）</h5>
                                        <Progress
                                            percent={19}
                                            strokeColor={{
                                                from: "#EDC50C",
                                                to: "#EDC50C",
                                            }}
                                            strokeWidth="8px"
                                            trailColor="rgba(249, 249, 250, 0.08)"
                                            format={(value) => {
                                                return value + "/0";
                                            }}
                                        />
                                    </div>
                                    <div className="item-content">
                                        <h5>设备报废量（台）</h5>
                                        <Progress
                                            percent={1}
                                            strokeColor={{
                                                from: "#B6BECC",
                                                to: "#B6BECC",
                                            }}
                                            strokeWidth="8px"
                                            trailColor="rgba(249, 249, 250, 0.08)"
                                            format={(value) => {
                                                return value;
                                            }}
                                        />
                                    </div>
                                </div>
                                <img className="QC-data-cycle" src={cycle_bg} />
                            </div>
                        </div>
                    </div>
                    <div className="QC-system-section">
                        <h3 className="QC-system-title">
                            医疗设备质量控制体系
                        </h3>
                        <QCSystemCarousel />
                    </div>
                    <div className="QC-empty-section">
                        <p>
                            依据《放射科管理规范与质控标准》（2017版）、《放射科治疗质量控制基本指南》、《放射科诊疗管理规定》，医疗设备质量控制管理目标：
                        </p>
                        <div className="item-ad">
                            <div className="item-icon">
                                <img src={icon_5} />
                            </div>
                            <span>确保设备安全与稳定</span>
                        </div>
                        <div className="item-ad">
                            <div className="item-icon">
                                <img src={icon_6} />
                            </div>

                            <span>提高影像质量与诊断准确性</span>
                        </div>
                        <div className="item-ad">
                            <div className="item-icon">
                                <img src={icon_7} />
                            </div>
                            <span>提供工作效率与服务质量</span>
                        </div>
                        <div className="item-ad">
                            <div className="item-icon">
                                <img src={icon_8} />
                            </div>
                            <span>持续改进与技术创新</span>
                        </div>
                        <div className="item-ad">
                            <div className="item-icon">
                                <img src={icon_9} />
                            </div>
                            <span>遵循法规和标准</span>
                        </div>
                    </div>
                </div>
            </Content>
        </Layout>
    );
}

const useRadQCBoard = () => {
    const [radioValue, setRadioValue] = useState("year");
    const [filterTime, setFilterTime] = useState([
        moment().startOf("year"),
        moment(),
    ]);
    const selectTimeScope = (timeScope) => {
        let startTime, endTime;
        setRadioValue(timeScope);
        switch (timeScope) {
            case "week":
                startTime = moment().subtract(7, "days");
                endTime = moment().endOf("day");
                break;
            case "month":
                startTime = moment().subtract(30, "days");
                endTime = moment().endOf("day");
                break;
            case "quarter":
                startTime = moment().subtract(90, "days");
                endTime = moment().endOf("day");

                break;
            case "year":
                startTime = moment().startOf("year");
                endTime = moment().endOf("day");

                break;
            default:
                break;
        }
        setFilterTime([startTime, endTime]);
    };

    useEffect(() => {
        const baseWidth = 1920;
        const baseHeight = 1093;
        const scalePage = () => {
            let scaleX = window.innerWidth / baseWidth;
            let scaleY = window.innerHeight / baseHeight;
            let scale;
            if (window.innerWidth > baseWidth) {
                // 放大，宽度超过1920时允许放大
                scale = Math.min(scaleX, scaleY);
            } else {
                // 缩放，宽度不超过1920时正常缩放
                scale = Math.min(scaleX, scaleY);
            }
            const root = document.querySelector(".RadQCBoard");
            if (root) {
                root.style.transform = `scale(${scale})`;
                root.style.transformOrigin = "left top";
                root.style.width = `${baseWidth}px`;
                root.style.height = `${baseHeight}px`;
            }
        };
        // scalePage();
        // window.addEventListener("resize", scalePage);
        // return () => {
        //     window.removeEventListener("resize", scalePage);
        // };
    }, []);

    useEffect(() => {
        console.log(
            "filterTime0==",
            moment(filterTime[0]).format("YYYY-MM-DD"),
            "filterTime1==",
            moment(filterTime[1]).format("YYYY-MM-DD")
        );
    }, [filterTime]);

    return {
        selectTimeScope,
        radioValue,
        filterTime,
    };
};

export default RadQCBoard;
