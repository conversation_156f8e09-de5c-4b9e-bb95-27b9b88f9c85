import React, { useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import { Button, Modal, Tabs, Form, Input, message } from 'antd'
import { rest, urls, route_urls } from '@constants'
import { systemActions } from '@actions'
import { browserHistory as history } from 'react-router'
import { CodeButton } from '@/containers/user/Register'

import './RegisterLogin.less'
const { TabPane } = Tabs

function RegisterLogin(props) {
  return (
    <div className="RegisterLogin">
      <Modal
        visible={props.visible}
        maskClosable={props.maskClosable}
        getContainer={props.getContainer}
        onCancel={() => {
          props.onCancel()
        }}
        footer={null}
        bodyStyle={{ padding: '5px 24px' }}
      >
        <Tabs>
          <TabPane tab="密码登录" key="user_login" className="user_login" forceRender={true}>
            <div style={{ paddingBottom: '10px' }}>请使用APM-远程守护账号登录</div>
            <FormLogin close={props.onCancel}></FormLogin>
          </TabPane>
          <TabPane tab="注册登录" key="user_register" className="user_register" forceRender={true}>
            <FormRegister close={props.onCancel}></FormRegister>
          </TabPane>
        </Tabs>
      </Modal>
    </div>
  )
}
/**
 * 登录
 */
const FormLogin = Form.create()(props => {
  const { getFieldDecorator } = props.form
  const weChatId = useSelector(state => state.preload.wxUserInfo && state.preload.wxUserInfo.openId)
  const onsubmit = e => {
    e.preventDefault()
    props.form.validateFields((err, values) => {
      const pathBeforeLogin = JSON.parse(JSON.stringify(location))
      if (values.password !== '' && values.loginName !== '') {
        systemActions.login(
          {
            password: values.password,
            newPassword: values.password,
            loginName: values.loginName,
            weChatId
          },
          pathBeforeLogin
        )
      }
    })
  }

  return (
    <Form onSubmit={onsubmit}>
      <Form.Item label="用户名">
        {getFieldDecorator('loginName', {
          initialValue: '',
          rules: [{ required: true, message: '用户名不能为空' }]
        })(<Input placeholder="请输入用户名" />)}
      </Form.Item>
      <Form.Item label="密码">
        {getFieldDecorator('password', {
          initialValue: '',
          rules: [{ required: true, message: '密码不能为空' }]
        })(<Input type="password" placeholder="请输入密码" />)}
      </Form.Item>
      <Form.Item>
        <div className="submitButton" style={{ display: 'flex', justifyContent: 'space-between' }}>
          <Button type="primary" htmlType="submit" className="button">
            登录
          </Button>
          <Button
            className="button"
            onClick={() => {
              props.close()
            }}
          >
            访客继续访问
          </Button>
        </div>
      </Form.Item>
    </Form>
  )
})

/**
 * 注册
 */
const FormRegister = Form.create()(props => {
  const { getFieldDecorator, getFieldValue } = props.form
  const weChatId = useSelector(state => state.preload.wxUserInfo && state.preload.wxUserInfo.openId)
  const onRegister = e => {
    e.preventDefault()
    props.form.validateFields((err, values) => {
      if (values.phonenumber !== '' && values.verifycode !== '') {
        const data = {
          phonenumber: values.phonenumber,
          verifycode: values.verifycode,
          openid: weChatId
        }
        rest
          .post(urls.simpleReg, data)
          .then(res => {
            if (res.status != 1) {
              message.error(res.status)
            } else {
              props.close()
              //history.push({ pathname: route_urls.registerResult, state: { type: 'simple' } })
            }
          })
          .catch(() => {
            // message.error(res)
          })
      }
    })
  }

  const validateTelephone = telephoneNumber => {
    if (telephoneNumber == '' || telephoneNumber.match(/[0-9+()-]+/) == null || telephoneNumber.length != 11) {
      return {
        status: 'error',
        msg: '请输入合法的电话号码'
      }
    } else if (telephoneNumber.match(/\s/)) {
      return {
        status: 'error',
        msg: '不能包含空格'
      }
    } else {
      return {
        status: '',
        msg: ''
      }
    }
  }

  return (
    <Form className="login-form" onSubmit={onRegister}>
      <Form.Item label="手机号" validateStatus={validateTelephone.status} help={validateTelephone.msg}>
        {getFieldDecorator('phonenumber', {
          rules: [{ required: true, message: '手机号不能为空' }],
          initialValue: ''
        })(<Input placeholder="请输入11位手机号" />)}
      </Form.Item>
      <Form.Item label="验证码">
        <div style={{ display: 'flex', justifyContent: 'space-between', gap: '5px' }}>
          {getFieldDecorator('verifycode', {
            rules: [{ required: true, message: '请输入获取的6位验证码', len: 6 }],
            initialValue: ''
          })(<Input placeholder="请输入验证码" />)}
          <CodeButton validation={validateTelephone} value={getFieldValue('phonenumber')} />
          {/* <Button loading={loading} onClick={getVerificationCode}>
            获取验证码
          </Button> */}
        </div>
      </Form.Item>
      <div style={{ paddingBottom: '15px', color: 'rgba(0, 0, 0, 0.45)' }}>
        未注册手机验证后自动登录，登录即代表注册GE资产智管家
      </div>
      <Form.Item>
        <div className="submitButton" style={{ display: 'flex', justifyContent: 'space-between' }}>
          <Button type="primary" htmlType="submit" className="button">
            注册
          </Button>
          <Button
            className="button"
            onClick={() => {
              props.close()
            }}
          >
            访客继续访问
          </Button>
        </div>
      </Form.Item>
    </Form>
  )
})
export default RegisterLogin
